#!/bin/bash

# Safe sequential Docker build script with optimized caching
# Use this if parallel builds cause cache conflicts

set -e

echo "🚀 Starting optimized sequential Docker builds..."

# Enable BuildKit for better caching
export DOCKER_BUILDKIT=1

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to build with cache
build_with_cache() {
    local dockerfile=$1
    local tag=$2
    local context=$3
    local app_name=$4
    
    echo -e "${BLUE}📦 Building $app_name...${NC}"
    
    # Use buildx for better caching
    docker buildx build \
        --file "$dockerfile" \
        --tag "$tag" \
        --cache-from "type=local,src=/tmp/.buildx-cache-$app_name" \
        --cache-to "type=local,dest=/tmp/.buildx-cache-$app_name,mode=max" \
        --load \
        "$context" && \
    echo -e "${GREEN}✅ Built $tag${NC}" || \
    (echo -e "${RED}❌ Failed to build $tag${NC}" && exit 1)
}

# Create cache directories
mkdir -p /tmp/.buildx-cache-migrate /tmp/.buildx-cache-backend /tmp/.buildx-cache-frontend

# Start builds sequentially but with optimized caching
echo -e "${YELLOW}🔧 Starting sequential builds with optimized caching...${NC}"

start_time=$(date +%s)

# Build migrate
build_with_cache "apps/migrate/Dockerfile" "warda/migrate:latest" "." "migrate"

# Build backend
build_with_cache "apps/backend/Dockerfile" "warda/backend:latest" "." "backend"

# Build frontend
build_with_cache "apps/frontend/Dockerfile" "warda/frontend:latest" "." "frontend"

end_time=$(date +%s)
duration=$((end_time - start_time))

echo ""
echo -e "${GREEN}🎉 All builds completed successfully in ${duration}s!${NC}"
echo ""
echo -e "${BLUE}💡 Performance Tips:${NC}"
echo "  - Cache directories created in /tmp/.buildx-cache-*"
echo "  - Subsequent builds will be much faster"
echo "  - Use 'docker system prune' to clean up if needed"
echo "  - Try parallel script again after cache is warmed up"
